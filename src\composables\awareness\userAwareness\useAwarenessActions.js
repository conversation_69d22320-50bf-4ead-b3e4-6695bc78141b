import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import AwarenessService from '@/services/awareness/AwarenessService';
import Swal from 'sweetalert2';

export const useAwarenessActions = () => {
  const router = useRouter();
  const loading = ref(false);
  const courses = ref([]);
  const currentCourse = ref(null);
  const topics = ref([]);
  const currentLesson = ref(null);
  const courseProgressCache = new Map();
  const questions = ref([]);
  const quizResult = ref(null);
  const currentQuestionIndex = ref(0);
  const totalCertificates = computed(() => courses.value?.filter(course => course.certificate_eligible)?.length || 0);
  const completedCoursesCount = computed(() => courses.value?.filter(course => (course.completion_percentage || 0) === 100)?.length || 0);

  // general helper functions
  const withLoading = async (operation) => {
    loading.value = true;
    try {
      return await operation();
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  const showError = (message) => {
    return Swal.fire({
      title: 'Error',
      text: message || 'An error occurred.',
      icon: 'error',
      confirmButtonColor: '#059669'
    });
  };

  //awareness evaluation dashboard related functions
  const enhanceCourseWithProgress = async (course) => {
    if (courseProgressCache.has(course._id)) {
      return {...course,...courseProgressCache.get(course._id)};
    }

    const enhancedData = {
      completion_percentage: 0,
      total_lessons: 0,
      completed_lessons: 0,
      certificate_eligible: false,
      last_activity: course.created_at
    };

    try {
      const { data: topics } = await AwarenessService.getCourseTopics(course._id);
      if (topics && topics.length > 0) {
        let totalLessons = 0;
        let completedLessons = 0;
        let hasCompletedFinalExam = false;

        topics.forEach(topic => {
          if (topic.lessons && Array.isArray(topic.lessons)) {
            topic.lessons.forEach(lesson => {
              totalLessons++;
              if (lesson.completed === 1) {
                completedLessons++;
                if (lesson.is_final_exam === '1' || lesson.lesson_type === 'final_exam') hasCompletedFinalExam = true;
              }
            });
          }
        });

        enhancedData.total_lessons = totalLessons;
        enhancedData.completed_lessons = completedLessons;
        enhancedData.completion_percentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0;
        enhancedData.certificate_eligible = (completedLessons === totalLessons && hasCompletedFinalExam);
        
        courseProgressCache.set(course._id, enhancedData);
      }
    } catch (error) {
      console.error('Error fetching course topics for progress:', error);
    }

    return {
      ...course,
      ...enhancedData
    };
  };

  const getAwarenessEvaluationsWithProgress = async () => {
    return withLoading(async () => {
      const { data: rawCourses } = await AwarenessService.getAwarenessEvaluations();
      
      const enhancedCourses = [];
      for (const course of rawCourses) {
        try {
          const enhancedCourse = await enhanceCourseWithProgress(course);
          enhancedCourses.push(enhancedCourse);
        } catch (error) {
          console.error('Error enhancing course data for:', course._id, error);
          enhancedCourses.push({
            ...course,
            completion_percentage: 0,
            total_lessons: 0,
            completed_lessons: 0,
            certificate_eligible: false,
            last_activity: course.created_at || new Date().toISOString()
          });
        }
      }
      courses.value = enhancedCourses;
    });
  };

  const getProgressClass = (percentage) => {
    if (percentage === 100) return 'progress-complete';
    if (percentage > 50) return 'progress-good';
    if (percentage > 0) return 'progress-started';
    return 'progress-none';
  };

  const getStatusIcon = (completionPercentage) => {
    if (completionPercentage === 100) return 'fa-check-circle';
    else if (completionPercentage > 0) return 'fa-play-circle';
    else return 'fa-circle-o';
  };

  const getStatusLabel = (completionPercentage) => {
    if (completionPercentage === 100) return 'Completed';
    else if (completionPercentage > 0) return 'In Progress';
    else return 'Not Started';
  };

  const getStatusClass = (completionPercentage) => {
    if (completionPercentage === 100) return 'completed';
    else if (completionPercentage > 0) return 'in-progress';
    else return 'not-started';
  };

  const getProgressColor = (percentage) => {
    if (percentage === 100) return '#10b981';
    if (percentage > 50) return '#f59e0b';
    if (percentage > 0) return '#3b82f6';
    return '#e5e7eb';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  //Course List related functions
  const getAvailableCourses = async () => {
    return withLoading(async () => {
      const { data } = await AwarenessService.getAvailableCourses();
      courses.value = data;
      return { data };
    });
  };
  
  const canAccessCourse = (course) => {
    const currentPackage = JSON.parse(localStorage.getItem("Subscription"));
    return course.package.package_level <= currentPackage.package_level;
  };

  const startCourse = (course) => {
    if (!canAccessCourse(course)) {
      console.error('This course requires a higher subscription level. Please upgrade your plan.');
      router.push({ name: 'Pricing' });
      return;
    }
    router.push({ name: 'AwarenessCoursePreview', params: { course_id: course._id } });
  };

  //course preview related functions
  const getSingleCourse = async (courseId) => {
    return withLoading(async () => {
      const { data } = await AwarenessService.getSingleCourse(courseId);
      currentCourse.value = data;
      return { data };
    });
  };

  const getCourseTopics = async (courseId) => {
    return withLoading(async () => {
      const { data } = await AwarenessService.getCourseTopics(courseId);
      topics.value = data;
      return { data };
    });
  };

  const getTotalLessons = (topics) => {
    return topics.reduce((total, topic) => {
      return total + (topic.lessons ? topic.lessons.length : 0);
    }, 0);
  };

  const getCompletedLessons = (topics) => {
    return topics.reduce((total, topic) => {
      if (!topic.lessons) return total;
      return total + topic.lessons.filter(lesson => lesson.completed === 1).length;
    }, 0);
  };
  
  const getOverallProgress = (topics) => {
    if (!topics || !topics.length) return 0;
    const totalLessons = getTotalLessons(topics);
    const completedLessons = getCompletedLessons(topics);
    return totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;
  };

  const getTopicProgress = (topic) => {
    if (!topic.lessons) return '0/0';
    const totalLessons = topic.lessons.length;
    const completedLessons = topic.lessons.filter(lesson => lesson.completed === 1).length;
    return `${completedLessons}/${totalLessons}`;
  };

  const getFirstAvailableLesson = (topics) => {
    for (const topic of topics) {
      if (!topic.lessons) continue;
      for (const lesson of topic.lessons) {
        if (lesson.readable === 1) return { topic, lesson };
      }
    }
    return null;
  };

  const startFirstLesson = (course, topics) => {
    const firstLesson = getFirstAvailableLesson(topics);
    if (firstLesson) {
      router.push({ name: 'LessonPreview', params: { course_id: course._id, topic_id: firstLesson.topic._id, lesson_id: firstLesson.lesson._id } });
    }
  };

  const continueFromLastLesson = (course, topics) => {
    for (const topic of topics) {
      if (!topic.lessons) continue;
      for (const lesson of topic.lessons) {
        if (lesson.readable === 1 && lesson.completed === 0) {
          router.push({ name: 'LessonPreview', params: { course_id: course._id, topic_id: topic._id, lesson_id: lesson._id } });
          return;
        }
      }
    }
  };

  const showCertificateDownloaded = () => {
    return Swal.fire({
      title: 'Certificate Downloaded!',
      text: 'Your certificate has been downloaded successfully.',
      icon: 'success',
      timer: 2000,
      showConfirmButton: false
    });
  };

  const downloadCourseCertificate = async (courseId) => {
    return withLoading(async () => {
      const response = await AwarenessService.downloadCourseCertificate(courseId);
      
      const disposition = response.headers['content-disposition'];
      let filename = 'certificate.pdf';
      if (disposition && disposition.includes('filename=')) {
        const filenameMatch = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch != null && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '');
        }
      }

      const file = new Blob([response.data], { type: 'application/pdf' });
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(file);
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      showCertificateDownloaded();

      return response;
    });
  };

  //lesson preview + quiz taking related functions
  const resetQuizData = (clearQuestions = false) => {
    if (clearQuestions) questions.value = [];
    else questions.value.forEach(q => q.answer = null);
    quizResult.value = null;
    currentQuestionIndex.value = 0;
  };

  const getSingleLesson = async (courseId, topicId, lessonId, forceRefresh = false) => {
    if (currentLesson.value?.id === lessonId && !forceRefresh) {
      return { data: currentLesson.value };
    }

    return withLoading(async () => {
      resetQuizData(true);
      const { data } = await AwarenessService.getSingleLesson(courseId, topicId, lessonId);

      currentLesson.value = data.lesson;

      if (data.lesson?.is_quiz === '1') {
        quizResult.value = data.quiz_result;
        questions.value = data.lesson.questions || [];
        currentQuestionIndex.value = 0;
      }

      return { data };
    });
  };

  const confirmMarkAsComplete = () => {
    return Swal.fire({
      title: 'Mark as Complete',
      text: "Are you sure you want to mark this lesson as complete?",
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#059669',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, Complete',
      cancelButtonText: 'Cancel',
      customClass: {
        popup: 'rounded-lg',
        confirmButton: 'rounded-lg',
        cancelButton: 'rounded-lg'
      }
    });
  };

  const completeLesson = async (courseId, topicId, lessonId) => {
    return withLoading(async () => {
      const { data, msg } = await AwarenessService.completeLesson(courseId, topicId, lessonId);
      return { data, msg };
    });
  };

  const showLessonCompleted = (message) => {
    return Swal.fire({
      title: 'Completed!',
      text: message || 'Lesson marked as complete successfully.',
      icon: 'success',
      timer: 2000,
      showConfirmButton: false
    });
  };

  const markAsComplete = async (courseId, topicId, lessonId, onRefresh = null) => {
    const result = await confirmMarkAsComplete();

    if (result.isConfirmed) {
      try {
        const { msg } = await completeLesson(courseId, topicId, lessonId);
        showLessonCompleted(msg);
        await getSingleLesson(courseId, topicId, lessonId, true);
        if (onRefresh) onRefresh({ topic_id: topicId, lesson_id: lessonId });
      } catch (error) {
        showError('Failed to mark lesson as complete.');
      }
    }
  };

  const prevQuestion = () => {
    if (currentQuestionIndex.value > 0) currentQuestionIndex.value--;
  };

  const nextQuestion = () => {
    if (currentQuestionIndex.value < questions.value.length - 1) currentQuestionIndex.value++;
  };

  const goToQuestion = (index) => {
    if (index >= 0 && index < questions.value.length) currentQuestionIndex.value = index;
  };

  const allQuestionsAnswered = (questionsArray = null) => {
    const questionsToCheck = questionsArray || questions.value;
    return questionsToCheck.every(q => q.answer);
  };

  const showIncompleteQuiz = () => {
    return Swal.fire({
      title: 'Incomplete Quiz',
      text: 'Please answer all questions before submitting.',
      icon: 'warning',
      confirmButtonColor: '#059669'
    });
  };

  const confirmSubmitQuiz = () => {
    return Swal.fire({
      title: 'Submit Quiz',
      text: "Are you sure you want to submit your answers? You cannot change them after submission.",
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#059669',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Submit Quiz',
      cancelButtonText: 'Review Answers'
    });
  };

  const showQuizSubmitted = (score) => {
    return Swal.fire({
      title: 'Quiz Submitted!',
      text: `You scored ${score}%`,
      icon: score >= 80 ? 'success' : 'info',
      confirmButtonColor: '#059669'
    });
  };

  const submitQuiz = async (courseId, topicId, lessonId, onRefresh = null) => {
    if (!allQuestionsAnswered()) {
      showIncompleteQuiz();
      return;
    }
    const result = await confirmSubmitQuiz();
    if (result.isConfirmed) {
      try {
        const { data, msg } = await withLoading(async () => await AwarenessService.submitQuiz(courseId, topicId, lessonId, questions.value));
        console.log(data);
        if (data) {
          quizResult.value = data;
          showQuizSubmitted(data.score);
          if (onRefresh) onRefresh({ topic_id: topicId, lesson_id: lessonId });
          return { data, msg };
        }
      } catch (error) {
        showError('Failed to submit quiz.');
      }
    }
  };

  const confirmRetakeQuiz = () => {
    return Swal.fire({
      title: 'Retake Quiz',
      text: "Are you sure you want to retake this quiz? Your previous score will be replaced.",
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#059669',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Retake Quiz',
      cancelButtonText: 'Cancel'
    });
  };

  const retakeQuiz = async () => {
    const result = await confirmRetakeQuiz();
    if (result.isConfirmed) resetQuizData();
  };

  // quiz results related functions
  const getScoreClass = (score) => {
    if (score >= 90) return 'excellent';
    if (score >= 80) return 'good';
    if (score >= 70) return 'average';
    return 'needs-improvement';
  };

  const getScoreMessage = (score) => {
    if (score >= 90) return 'Excellent Work!';
    if (score >= 80) return 'Great Job!';
    if (score >= 70) return 'Good Effort!';
    return 'Keep Learning!';
  };

  const getScoreDescription = (score) => {
    if (score >= 90) return 'Outstanding performance! You have mastered this topic.';
    if (score >= 80) return 'Well done! You have a solid understanding of the material.';
    if (score >= 70) return 'Good work! Consider reviewing the material to strengthen your knowledge.';
    return 'Don\'t give up! Review the material and try again.';
  };

  return {
    loading,
    courses,
    currentCourse,
    topics,
    currentLesson,
    totalCertificates,
    completedCoursesCount,
    questions,
    quizResult,
    currentQuestionIndex,
    getAwarenessEvaluationsWithProgress,
    getAvailableCourses,
    getSingleCourse,
    getCourseTopics,
    getSingleLesson,
    startCourse,
    downloadCourseCertificate,
    markAsComplete,
    submitQuiz,
    allQuestionsAnswered,
    prevQuestion,
    nextQuestion,
    goToQuestion,
    retakeQuiz,
    getOverallProgress,
    getTotalLessons,
    getCompletedLessons,
    getTopicProgress,
    getFirstAvailableLesson,
    startFirstLesson,
    continueFromLastLesson,
    getStatusIcon,
    getStatusLabel,
    getProgressClass,
    getStatusClass,
    getProgressColor,
    formatDate,
    getScoreClass,
    getScoreMessage,
    getScoreDescription,
  };
};